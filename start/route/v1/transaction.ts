import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const TransactionController = () => import('#controllers/transaction_controller')

export default function transactionRoutes() {
  router
    .group(() => {
      // Public routes (if any)
      router.get('/stats', [TransactionController, 'stats'])

      // Protected routes
      router
        .group(() => {
          router.get('/', [TransactionController, 'index'])
          router.get('/:id', [TransactionController, 'show'])
          router.get('/order/:orderId', [TransactionController, 'getByOrder'])
          router.post('/:id/refund', [TransactionController, 'processRefund'])
        })
        .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
    })
    .prefix('transactions')
}
