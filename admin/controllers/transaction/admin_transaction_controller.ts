import { TransactionService } from '#services/transaction_service'
import {
  listTransactionsValidator,
  getTransactionValidator,
  getTransactionsByOrderValidator,
  processRefundValidator,
} from '#validators/transaction_validator'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'

export default class AdminTransactionController {
  private transactionService = new TransactionService()

  /**
   * Get all transactions with pagination and filtering
   */
  async index({ request, response }: HttpContext) {
    try {
      const { page, limit, source, status, orderId, payerEmail } =
        await request.validateUsing(listTransactionsValidator)

      const filters = {
        source: source || undefined,
        status: status || undefined,
        orderId: orderId || undefined,
        payerEmail: payerEmail || undefined,
      }

      const transactions = await this.transactionService.getAllTransactions(
        page || 1,
        limit || 10,
        filters
      )

      return response.ok(transactions)
    } catch (error) {
      logger.error('Failed to get transactions', { error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get transactions',
        error: error.message,
      })
    }
  }

  /**
   * Get transaction by ID
   */
  async show({ params, response }: HttpContext) {
    try {
      const { id } = await getTransactionValidator.validate({ id: params.id })

      const transaction = await this.transactionService.getTransactionById(id)

      return response.ok(transaction)
    } catch (error) {
      logger.error('Failed to get transaction', { id: params.id, error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get transaction',
        error: error.message,
      })
    }
  }

  /**
   * Get transactions by order ID
   */
  async getByOrder({ params, response }: HttpContext) {
    try {
      const { orderId } = await getTransactionsByOrderValidator.validate({
        orderId: params.orderId,
      })

      const transactions = await this.transactionService.getTransactionsByOrderId(orderId)

      return response.ok(transactions)
    } catch (error) {
      logger.error('Failed to get transactions by order', {
        orderId: params.orderId,
        error: error.message,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to get transactions by order',
        error: error.message,
      })
    }
  }

  /**
   * Process refund for a transaction
   */
  async processRefund({ params, response }: HttpContext) {
    console.log('Refund endpoint called with ID:', params.id)
    try {
      const { id } = await processRefundValidator.validate({ id: params.id })

      const refundResult = await this.transactionService.processRefund(id)

      return response.ok({
        success: true,
        message: 'Refund processed successfully',
        data: refundResult,
      })
    } catch (error) {
      logger.error('Failed to process refund', {
        id: params.id,
        error: error.message,
        stack: error.stack,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to process refund',
        error: error.message,
        details: error.stack,
      })
    }
  }

  /**
   * Get transaction statistics
   */
  async stats({ response }: HttpContext) {
    try {
      const stats = await this.transactionService.getTransactionStats()

      return response.ok(stats)
    } catch (error) {
      logger.error('Failed to get transaction stats', { error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get transaction stats',
        error: error.message,
      })
    }
  }
}
