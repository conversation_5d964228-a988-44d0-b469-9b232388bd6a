import router from '@adonisjs/core/services/router'
import adminManagementRoutes from './admin/admin_router.js'
import adminAuthRoutes from './auth/auth_router.js'
import adminCampaignRoutes from './campaign/campaign_router.js'
import adminCancelReasonsRoutes from './cancel-reasons/cancel_reasons_router.js'
import adminCategoryPagesRoutes from './category-pages/category_pages_router.js'
import adminCollectionRoutes from './collection/collection_router.js'
import adminContactInformationRoutes from './contact-information/contact_information_router.js'
import adminDashboardClassifiedRoutes from './dashboard/classified/dashboard_classified_router.js'
import adminDashboardEcomerceRoutes from './dashboard/ecomerce/dashboard_ecomerce_route.js'
import adminDrawerMenusRoutes from './drawer-menus/drawer_menus_router.js'
import adminEventRoutes from './event/event_router.js'
import adminGiftRoutes from './gift/gift_router.js'
import adminGPTRoutes from './gtp/gpt.js'
import adminHomeLayoutRoutes from './home-layout/home_layout_router.js'
import adminMeRoutes from './me/me_router.js'
import adminMediaRoutes from './media/media_router.js'
import adminNailSystemsRoutes from './nail-systems/nail_systems_router.js'
import adminOrderRoutes from './order/order_router.js'
import adminPostCategoryRoutes from './post-category/post_category_router.js'
import adminPostRoutes from './post/post.js'
import adminProductVariantRoutes from './product-variant/product_variant_router.js'
import adminProductRoutes from './product/product_router.js'
import adminProductTagRoutes from './product-organization/product_tag_router.js'
import adminProductTypeRoutes from './product-organization/product_type_router.js'
import adminProductVendorRoutes from './product-organization/product_vendor_router.js'
import adminReportRoutes from './report/report_router.js'
import adminRoleRoutes from './role/role_router.js'
import adminClaimStoreRoutes from './store/claim_store_router.js'
import adminStoreRoutes from './store/store_router.js'
import adminUserRoutes from './user/user.js'
import adminZurnoPostRoutes from './post/zurno_post_router.js'
import adminPermissionRoutes from './permissions/permission_router.js'
import adminDashboardAnalyticRoutes from './dashboard/analytic/dashboard_analytic_router.js'
import adminWarehousesRoutes from './warehouses/warehouses_router.js'
import adminSuppliersRoutes from './suppliers/suppliers_router.js'
import adminInventoryMovementsRoutes from './inventory-movements/inventory_movements_router.js'
import adminProductCategoriesRoutes from './product-categories/product_categories_router.js'
import adminStreamPostRoutes from './post/stream_posts_router.js'
import adminChatRoutes from './chat/chat_router.js'
import adminAffiliationRoutes from './affiliation/affiliation_router.js'
import adminNotificationsRoutes from './notifications/notifications_router.js'
import adminCommentsRoutes from './comments/comments_router.js'
import adminStoreSettingRoutes from './store/setting_router.js'
import adminLocationRoutes from './location/location_router.js'
import adminShippingCheckoutRoutes from './order/shipping_checkout_router.js'
import adminRewardProgramRoutes from './reward-program/reward_program_router.js'
import adminRewardPointRoutes from './reward-point/reward_point_router.js'
import adminVendorRoutes from './vendors/vendors_router.js'
import adminChatbotRoutes from './chatbot/chat_bot_router.js'
import adminSalonConstructionServiceRoutes from './salon_construction_service/salon_construction_service_router.js'
import adminTransactionRoutes from './transaction/transaction_router.js'

export default function adminRoutes() {
  router
    .group(() => {
      adminAuthRoutes()
      adminManagementRoutes()
      adminMediaRoutes()
      adminMeRoutes()
      adminPostRoutes()
      adminPostCategoryRoutes()
      adminNailSystemsRoutes()
      adminHomeLayoutRoutes()
      adminDrawerMenusRoutes()
      adminContactInformationRoutes()
      adminCategoryPagesRoutes()
      adminCancelReasonsRoutes()
      adminClaimStoreRoutes()
      adminStoreRoutes()
      adminUserRoutes()
      adminRoleRoutes()
      adminCampaignRoutes()
      adminReportRoutes()
      adminDashboardClassifiedRoutes()
      adminDashboardEcomerceRoutes()
      adminZurnoPostRoutes()
      adminOrderRoutes()
      adminGiftRoutes()
      adminGPTRoutes()
      adminProductRoutes()
      adminCollectionRoutes()
      adminProductVariantRoutes()
      adminProductTagRoutes()
      adminProductTypeRoutes()
      adminProductVendorRoutes()
      adminPermissionRoutes()
      adminDashboardAnalyticRoutes()
      adminWarehousesRoutes()
      adminSuppliersRoutes()
      adminInventoryMovementsRoutes()
      adminProductCategoriesRoutes()
      adminStreamPostRoutes()
      adminChatRoutes()
      adminAffiliationRoutes()
      adminNotificationsRoutes()
      adminCommentsRoutes()
      adminStoreSettingRoutes()
      adminEventRoutes()
      adminLocationRoutes()
      adminShippingCheckoutRoutes()
      adminRewardProgramRoutes()
      adminRewardPointRoutes()
      adminVendorRoutes()
      adminChatbotRoutes()
      adminSalonConstructionServiceRoutes()
      adminTransactionRoutes()
    })
    .prefix('admin')
}
