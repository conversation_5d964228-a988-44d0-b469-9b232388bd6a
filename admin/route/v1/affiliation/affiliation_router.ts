/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminAffiliateCommissionController = () => import('#adminControllers/affiliation/affiliate_commission_controller')
const AdminAffiliationTierController = () => import('#adminControllers/affiliation/affiliation_tier_controller')
const AdminAffiliationTierCommissionGroupsController = () => import('#adminControllers/affiliation/affiliation_tier_commission_groups_controller')
const AdminAffiliationCommissionRateController = () => import('#adminControllers/affiliation/affiliation_commission_rate_controller')
const AdminAffiliateController = () => import('#adminControllers/affiliation/affiliate_controller')

export default function adminAffiliationRoutes() {
  router
    .group(() => {
      router.get('/', [AdminAffiliationTierController, 'index'])
      router.get('/:id', [AdminAffiliationTierController, 'show'])
      router.get('/:id/next', [AdminAffiliationTierController, 'showNextTier'])
      router.post('/', [AdminAffiliationTierController, 'store'])
      router.put('/:id', [AdminAffiliationTierController, 'update'])
      router.delete('/:id', [AdminAffiliationTierController, 'destroy'])

      router.get('/:id/commission-groups', [AdminAffiliationTierCommissionGroupsController, 'showCommissionGroups'])
      router.get('/:id/commission-groups/non-commission-products', [AdminAffiliationTierCommissionGroupsController, 'showNonCommissionProducts'])
      router.post('/:id/commission-groups', [AdminAffiliationTierCommissionGroupsController, 'storeCommissionGroup'])

      router.get('/:id/discounted-products', [AdminAffiliationTierController, 'showDiscountedProducts'])
      router.get('/:id/full-priced-products', [AdminAffiliationTierController, 'getFullPricedProducts'])
      router.patch('/:id/discount-collection-by-collection', [AdminAffiliationTierController, 'updateDiscountCollectionByCollection'])
      router.patch('/:id/discount-collection-by-products', [AdminAffiliationTierController, 'updateDiscountCollectionByProductIds'])

      router.group(() => {
        router.put('/:id', [AdminAffiliationTierCommissionGroupsController, 'updateCommissionGroup'])
        router.delete('/:id', [AdminAffiliationTierCommissionGroupsController, 'destroyCommissionGroup'])
        router.get('/:id/products', [AdminAffiliationTierCommissionGroupsController, 'showProductsOfCommissionGroups'])
        router.put('/:id/products', [AdminAffiliationTierCommissionGroupsController, 'updateProductsOfCommissionGroups'])

        router.get('/:id/commission-rates', [AdminAffiliationCommissionRateController, 'index'])
        router.post('/:id/commission-rates', [AdminAffiliationCommissionRateController, 'store'])

        router.group(() => {
          router.get('/:id', [AdminAffiliationCommissionRateController, 'show'])
          router.put('/:id', [AdminAffiliationCommissionRateController, 'update'])
          router.delete('/:id', [AdminAffiliationCommissionRateController, 'destroy'])
        }).prefix('commission-rates')

      }).prefix('commission-groups')

    })
    .prefix('affiliate-tiers')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.get('/', [AdminAffiliateController, 'index'])
      router.get('/:id', [AdminAffiliateController, 'show'])
      router.get('/:id/referred-customers', [AdminAffiliateController, 'getReferredCustomers'])
      router.get('/:id/referred-customers-count', [AdminAffiliateController, 'getReferredCustomersCount'])
      router.get('/:id/commissions', [AdminAffiliateController, 'getCommissions'])
      router.get('/:id/commission-stats', [AdminAffiliateController, 'stats'])
      router.get('/:id/payment-methods', [AdminAffiliateController, 'paymentMethods'])
      router.post('/', [AdminAffiliateController, 'store'])
      router.post('/:id/pay', [AdminAffiliateController, 'payForCommissions'])
      router.put('/:id', [AdminAffiliateController, 'update'])
      router.patch('/:id/preview-sync-data', [AdminAffiliateController, 'previewSyncData'])
      router.patch('/:id/proceed-sync-data', [AdminAffiliateController, 'proceedSyncData'])
      router.delete('/:id', [AdminAffiliateController, 'destroy'])
    })
    .prefix('affiliates')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.patch('/:id/sync-commission', [AdminAffiliateCommissionController, 'syncCommission'])
      router.patch('/sync-commissions', [AdminAffiliateCommissionController, 'syncAllCommissions'])

      router.get('/:id', [AdminAffiliateCommissionController, 'show'])
      router.put('/:id', [AdminAffiliateCommissionController, 'update'])
      router.patch('/:id', [AdminAffiliateCommissionController, 'setStatus'])
      router.get('/', [AdminAffiliateCommissionController, 'index'])
    })
    .prefix('commissions')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

}
