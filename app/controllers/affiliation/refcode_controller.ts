import ZnAffiliate from '#models/zn_affiliate'
import ZnUser from '#models/zn_user'
import { AffiliateRefCodeService } from '#services/affiliation/affiliation_refcode_service'
import { createRefCodeValidator } from '#validators/affiliation/refcode_validator'
import { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'

export default class RefCodeController {
  private refCodeService: AffiliateRefCodeService;

  constructor() {
    this.refCodeService = new AffiliateRefCodeService();
  }

  /**
   * @show
   * @tag Ref Code
   * @summary Get REF Code of current logged-in affiliate
   * @description Get REF Code details of the current logged-in affiliate
   * @responseBody 201 - <ZnAffiliateRefCode>
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async show({ auth, response }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser;
      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id);
      return this.refCodeService.getRefCode(affiliate.refCodeId);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Ref Code
   * @summary Create a new REF Code for the current logged-in affiliate
   * @description Create a new REF Code for the current logged-in affiliate
   * @requestBody {"code": "ABC123456"}
   * @responseBody 201 - <ZnAffiliateRefCode>
   * @responseBody 400 - Invalid input - Bad Request
   */
  async store({ request, response, auth }: HttpContext) {
    try {
      // @ts-ignore
      const user = auth.getUserOrFail().serialize() as ZnUser;
      const affiliate = await ZnAffiliate.findByOrFail('userId', user.id);
      const payload = await createRefCodeValidator.validate(request.all());
      return await this.refCodeService.createRefCode(affiliate, payload.code);
    } catch (error) {
      logger.debug('Error: %s', error.message);
      return response.badRequest(error)
    }
  }
}
