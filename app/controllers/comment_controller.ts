import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import SendNotificationPostCommentJob, {
  CommentJobType,
} from '#jobs/send_notification_post_comment_job'
import ZnPostComment from '#models/zn_post_comment'
import ZnPostCommentLike from '#models/zn_post_comment_like'
import ZnUser from '#models/zn_user'
import ZnStore from '#models/zn_store'
import { createPostComment, updatePostComment } from '#validators/comment_validator'
import { HttpContext } from '@adonisjs/core/http'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'

export default class CommentController {
  /**
   * @create
   * @tag Comment
   * @requestBody <ZnPostComment>.only(parentCommentId, content, postId, resourceId, resourceType)
   * @responseBody 200 - <ZnPostComment>
   */
  async create({ request, response, user }: HttpContext) {
    const payload = await request.validateUsing(createPostComment)

    try {
      let commentDto: any = {
        parentCommentId: payload.parentCommentId,
        content: payload.content,
        userId: user?.id,
        userType: ZnUser.name,
        totalLike: 0,
      }

      if (payload.parentCommentId) {
        const parentComment = await ZnPostComment.find(payload.parentCommentId)
        if (!parentComment) {
          return response.badRequest({ message: 'Parent comment not found' })
        }
        commentDto = {
          ...commentDto,
          resourceId: payload.resourceId ?? parentComment.resourceId,
          resourceType: payload.resourceType ?? parentComment.resourceType,
          postId: payload.postId,
        }
      } else if (payload.postId && payload.resourceType === RESOURCE_TYPE.POST) {
        commentDto = {
          ...commentDto,
          postId: payload.postId,
          resourceId: payload.postId,
          resourceType: RESOURCE_TYPE.POST,
        }
      } else if (payload.resourceId && payload.resourceType) {
        commentDto = {
          ...commentDto,
          resourceId: payload.resourceId,
          resourceType: payload.resourceType,
        }
      } else {
        return response.badRequest({ message: 'Resource ID and type are required' })
      }

      const created = await ZnPostComment.create(commentDto)
      return response.created(created)
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @update
   * @tag Comment
   * @requestBody <ZnPostComment>.only(content)
   * @responseBody 200 - <ZnPostComment>
   */
  async update({ params, request, response, user }: HttpContext) {
    const comment = await ZnPostComment.find(params.id)

    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }

    if (comment.userId !== user?.id) {
      return response.forbidden({ message: 'Access denied' })
    }

    const { content } = await request.validateUsing(updatePostComment)

    await comment
      .merge({
        content,
        lastEditedAt: DateTime.local(),
      })
      .save()

    return response.ok(comment)
  }

  /**
   * @list
   * @tag Comment
   */
  async list({ request, response, user }: HttpContext) {
    try {
      const { page = 1, limit = 10, resourceId, postId, sortBy } = request.qs()
      console.log(resourceId, postId)
      // Support old app version, remove later
      let queryColumn: string
      let queryValue: string | undefined

      if (postId) {
        queryColumn = 'postId'
        queryValue = postId
      } else if (resourceId) {
        queryColumn = 'resourceId'
        queryValue = resourceId
      } else {
        return response.badRequest({ message: 'Resource ID is required' })
      }

      const queryComments = ZnPostComment.query()
        .where(queryColumn, queryValue as string)
        .whereNull('parentCommentId')
        .withCount('children')
        .withCount('reactUsers')
        .preload('reactUsers', (query) => {
          if (user?.id) {
            query.where({ 'zn_users.id': user?.id })
          }
        })
        .preload('user', (query) => {
          query.select(['avatar', 'firstName', 'lastName', 'email'])
        })
        .preload('admin', (query) => {
          query.select(['name', 'phone'])
        })

      if (sortBy === 'latest') {
        queryComments.orderBy('createdAt', 'asc')
      } else {
        queryComments.orderBy('createdAt', 'desc')
      }

      const rawComments = await queryComments.paginate(page, limit)
      const comments = rawComments.toJSON()

      const data = await Promise.all(
        comments.data.map(async (comment) => {
          const item = comment.toJSON()
          delete item.reactUsers

          let storeId = null
          if (comment.userType === 'ZnAdmin') {
            const store = await ZnStore.query().where('isZurno', true).first()
            storeId = store?.id || null
          }

          return {
            isLike: user ? comment.reactUsers.length > 0 : false,
            isEdited: !!comment.lastEditedAt,
            storeId,
            ...item,
          }
        })
      )

      return response.ok({
        meta: comments.meta,
        data,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @children
   * @tag Comment
   */
  async children({ request, response, params, user }: HttpContext) {
    try {
      const { page = 1, limit = 10, sortBy } = request.qs()
      const parentCommentId = params.id

      const queryComments = ZnPostComment.query()
        .where({ parentCommentId })
        .withCount('reactUsers')
        .preload('reactUsers', (query) => {
          if (user) {
            query.where({
              'zn_users.id': user?.id,
            })
          }
        })
        .preload('user', (query) => {
          query.select(['avatar', 'firstName', 'lastName', 'email'])
        })
        .preload('admin', (query) => {
          query.select(['name', 'phone'])
        })

      if (sortBy === 'latest') {
        queryComments.orderBy('createdAt', 'asc')
      } else {
        queryComments.orderBy('createdAt', 'desc')
      }
      const rawComments = await queryComments.paginate(page, limit)
      const comments = rawComments.toJSON()
      const data = await Promise.all(
        comments.data.map(async (comment) => {
          const item = comment.toJSON()
          delete item.reactUsers

          let storeId = null
          if (comment.userType === 'ZnAdmin') {
            const store = await ZnStore.query().where('isZurno', true).first()
            storeId = store?.id || null
          }

          return {
            isLike: user ? comment.reactUsers.length > 0 : false,
            storeId,
            ...item,
          }
        })
      )

      return response.ok({
        meta: comments.meta,
        data,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @delete
   * @tag Comment
   * @responseBody 200 - Deleted successfully
   * @responseBody 404 - Comment not found
   */
  async delete({ params, response, user }: HttpContext) {
    const comment = await ZnPostComment.find(params.id)
    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }

    if (comment.userId !== user?.id) {
      return response.forbidden({ message: 'Access denied' })
    }

    try {
      await comment.softDelete()

      return response.ok({ message: 'Deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @react
   * @tag Comment
   * @responseBody 200 - Like successfully
   * @responseBody 404 - Comment not found
   */
  async react({ response, params, user }: HttpContext) {
    const comment = await ZnPostComment.find(params.id)
    const userId = user?.id

    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }
    if (!userId) {
      return response.forbidden({ message: 'Access denied' })
    }

    const like = await ZnPostCommentLike.query()
      .where({
        userId,
        postCommentId: params.id,
      })
      .first()

    if (like) {
      await like.delete()

      return response.ok('Unlike successfully')
    } else {
      await ZnPostCommentLike.create({
        userId,
        postCommentId: params.id,
      })
      await queue.dispatch(SendNotificationPostCommentJob, {
        comment,
        type: CommentJobType.Like,
        user,
      })

      return response.ok('Like successfully')
    }
  }

  /**
   * @show
   * @tag Comment
   * @responseBody 200 - Like successfully
   * @responseBody 404 - Comment not found
   */
  async show({ response, params }: HttpContext) {
    const comment = await ZnPostComment.query().where({ id: params.id }).preload('post').first()

    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }

    return response.ok(comment)
  }
}
